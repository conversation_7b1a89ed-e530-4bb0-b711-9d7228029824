import 'package:flutter/material.dart';
import 'upcoming.dart';
import 'client_list.dart';
import 'Registration.dart';
import 'Reports.dart';
import 'trends.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: const Color(0xFFC74E25),
        scaffoldBackgroundColor: Colors.grey.shade100,
      ),
      home: const FollowUpsPage(),
    );
  }
}

class FollowUpsPage extends StatefulWidget {
  const FollowUpsPage({Key? key}) : super(key: key);

  @override
  State<FollowUpsPage> createState() => _FollowUpsPageState();
}

class _FollowUpsPageState extends State<FollowUpsPage> {
  bool _isToday = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: const Text(
          'Follow-ups',
          style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Tab buttons for Today and Upcoming
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Expanded(child: _buildTabButton('Today', _isToday)),
                const SizedBox(width: 12),
                Expanded(child: _buildTabButton('Upcoming', !_isToday)),
              ],
            ),
          ),

          // Schedule buttons
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                _buildScheduleButton(
                  icon: Icons.access_time,
                  text: "Today's Schedule",
                  color: const Color(0xFFC74E25),
                ),
                const SizedBox(width: 12),
                _buildScheduleButton(
                  icon: Icons.calendar_today_outlined,
                  text: "Export Schedule",
                  color: const Color(0xFFC74E25),
                ),
              ],
            ),
          ),

          // Quick Actions heading
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: const Text(
              'Quick Actions',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              textAlign: TextAlign.left,
            ),
          ),

          // Quick Action buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.notifications_outlined,
                    text: 'Send Reminders',
                    color: const Color(0xFFFFF0DA),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickActionButton(
                    icon: Icons.phone_outlined,
                    text: 'Make Calls',
                    color: const Color(0xFFF3E5FF),
                  ),
                ),
              ],
            ),
          ),

          // Follow-up list
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              children: [
                _buildFollowUpCard(
                  name: 'Maria Garcia',
                  description: 'Contraceptive Refill',
                  time: '10:30 AM',
                  consultationPoints: [
                    'Review current contraceptive method satisfaction',
                    'Check for any side effects',
                    'Discuss any concerns or questions',
                  ],
                ),
                _buildFollowUpCard(
                  name: 'Elena Rodriguez',
                  description: 'Side Effect Check',
                  time: '11:45 AM',
                  consultationPoints: [
                    'Review current contraceptive method satisfaction',
                    'Check for any side effects',
                    'Discuss any concerns or questions',
                  ],
                ),
                _buildFollowUpCard(
                  name: 'Sofia Reyes',
                  description: 'General Follow-up',
                  time: '2:15 PM',
                  consultationPoints: [
                    'Review current contraceptive method satisfaction',
                    'Check for any side effects',
                    'Discuss any concerns or questions',
                  ],
                ),
                _buildFollowUpCard(
                  name: 'John Doe',
                  description: 'Method Counseling',
                  time: '3:30 PM',
                  status: 'completed',
                ),
                _buildFollowUpCard(
                  name: 'Ana Martinez',
                  description: 'Contraceptive Refill',
                  time: '4:45 PM',
                  status: 'missed',
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildTabButton(String text, bool isSelected) {
    return ElevatedButton(
      onPressed: () {
        if (text == 'Today') {
          setState(() {
            _isToday = true;
          });
        } else {
          // Navigate to Upcoming page
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const UpcomingScreen()),
          );
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? const Color(0xFFFFF0DA) : Colors.white,
        elevation: 0,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isSelected ? Colors.transparent : Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: isSelected ? const Color(0xFFC74E25) : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildScheduleButton({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Expanded(
      child: TextButton.icon(
        onPressed: () {},
        icon: Icon(icon, color: color, size: 18),
        label: Text(text, style: TextStyle(color: color, fontSize: 14)),
        style: TextButton.styleFrom(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.symmetric(vertical: 0),
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 28),
          const SizedBox(height: 4),
          Text(
            text,
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildFollowUpCard({
    required String name,
    required String description,
    required String time,
    List<String> consultationPoints = const [],
    String status = '',
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              status == 'completed'
                  ? Colors.green.shade100
                  : status == 'missed'
                  ? Colors.red.shade100
                  : Colors.orange.shade100,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        description,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                      if (time.isNotEmpty &&
                          status != 'completed' &&
                          status != 'missed')
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              time,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
                if (status == 'completed')
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      'Completed',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else if (status == 'missed')
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Text(
                      'Missed',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.green.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          size: 18,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          size: 18,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
          if (consultationPoints.isNotEmpty) ...[
            if (time.isNotEmpty &&
                status != 'completed' &&
                status != 'missed') ...[
              Padding(
                padding: const EdgeInsets.only(left: 12, right: 12),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      time,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 6),
            ],
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                'Consultation Points',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
              ),
            ),
            const SizedBox(height: 6),
            ...consultationPoints.map(
              (point) => Padding(
                padding: const EdgeInsets.only(left: 12, right: 12, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '• ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Expanded(
                      child: Text(point, style: const TextStyle(fontSize: 14)),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 6),
          ],
          if (status.isEmpty) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Add Notes',
                    style: TextStyle(
                      color: Color(0xFFC74E25),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(
              26,
            ), // Using withAlpha instead of withOpacity
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: 3,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: const Color(0xFFC74E25),
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        elevation: 0,
        backgroundColor: Colors.white,
        onTap: (index) {
          if (index == 0) {
            // Navigate to Home page
            Navigator.pop(context);
          } else if (index == 1) {
            // Navigate to Clients page
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ClientListPage()),
            );
          } else if (index == 2) {
            // Navigate to Register page
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ClientRegistrationPage(),
              ),
            );
          } else if (index == 3) {
            // Already on Follow-ups page
          } else if (index == 4) {
            // Show a dialog to choose between Reports Dashboard and Trends
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('Select Report Type'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListTile(
                        leading: Icon(
                          Icons.dashboard,
                          color: Colors.orange[700],
                        ),
                        title: const Text('Reports Dashboard'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const DashboardScreen(),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: Icon(
                          Icons.trending_up,
                          color: Colors.orange[700],
                        ),
                        title: const Text('Trends'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const TrendsScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people_outline),
            label: 'Clients',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_add_outlined),
            label: 'Register',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today),
            label: 'Follow-ups',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart_outlined),
            label: 'Reports',
          ),
        ],
      ),
    );
  }
}
