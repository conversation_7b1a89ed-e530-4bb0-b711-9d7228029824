{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter_windows_3.29.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\phone-app\\myapp\\android\\app\\.cxx\\Debug\\443l384i\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\phone-app\\myapp\\android\\app\\.cxx\\Debug\\443l384i\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}