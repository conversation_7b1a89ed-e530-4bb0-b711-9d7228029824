import 'package:flutter/material.dart';
import '../providers/auth_provider.dart';

// A widget that checks if the user has the required role before showing the child
class RoleGuard extends StatelessWidget {
  final Widget child;
  final List<String> allowedRoles;
  final Widget? fallbackWidget;

  const RoleGuard({
    Key? key,
    required this.child,
    required this.allowedRoles,
    this.fallbackWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authService = AuthProvider.of(context);
    
    // If not authenticated, redirect to login
    if (!authService.isAuthenticated) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/login');
      });
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    
    // If authenticated but doesn't have the required role
    if (!allowedRoles.contains(authService.currentUser!.role)) {
      if (fallbackWidget != null) {
        return fallbackWidget!;
      }
      
      // Default unauthorized screen
      return Scaffold(
        appBar: AppBar(
          title: const Text('Unauthorized'),
          backgroundColor: Colors.red,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.lock,
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Access Denied',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'You do not have permission to access this page.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  // Navigate back to appropriate home based on role
                  if (authService.isClient) {
                    Navigator.pushReplacementNamed(context, '/client-home');
                  } else if (authService.isHealthWorker) {
                    Navigator.pushReplacementNamed(context, '/healthworker-dashboard');
                  } else {
                    Navigator.pushReplacementNamed(context, '/login');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }
    
    // User has the required role, show the child
    return child;
  }
}
