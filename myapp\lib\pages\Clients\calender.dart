import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: const Color(0xFFFF7A5C),
        fontFamily: 'Roboto',
        scaffoldBackgroundColor: Colors.grey.shade50,
      ),
      home: const CycleTrackingCalendarPage(),
    );
  }
}

class CycleDay {
  final int day;
  final CycleDayType type;
  final String? note;
  CycleDay({required this.day, required this.type, this.note});
}

enum CycleDayType { normal, period, ovulation, fertile, predicted, today }

class CycleTrackingCalendarPage extends StatefulWidget {
  const CycleTrackingCalendarPage({Key? key}) : super(key: key);

  @override
  _CycleTrackingCalendarPageState createState() =>
      _CycleTrackingCalendarPageState();
}

class _CycleTrackingCalendarPageState extends State<CycleTrackingCalendarPage> {
  DateTime currentMonth = DateTime.now();
  DateTime today = DateTime.now();
  int? selectedDay;

  // Enhanced mapping of days to their cycle types
  final Map<int, CycleDayType> cycleDays = {
    // Period days
    4: CycleDayType.period,
    5: CycleDayType.period,
    6: CycleDayType.period,
    7: CycleDayType.period,
    8: CycleDayType.period,
    // Fertile window
    12: CycleDayType.fertile,
    13: CycleDayType.fertile,
    15: CycleDayType.fertile,
    16: CycleDayType.fertile,
    // Ovulation
    14: CycleDayType.ovulation,
    // Predicted next period
    32: CycleDayType.predicted,
    33: CycleDayType.predicted,
    34: CycleDayType.predicted,
  };

  final Map<int, String> dayNotes = {
    4: 'Heavy flow',
    7: 'Light flow',
    14: 'Ovulation day',
    20: 'Mood changes',
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(bottom: 20),
              child: Column(
                children: [
                  _buildCalendarHeader(),
                  _buildCalendarCard(),
                  _buildLegendCard(),
                  if (selectedDay != null) _buildDayDetailsCard(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 40, 20, 20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF7A5C), Color(0xFFFF6B47)],
        ),
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, '/cycle-tracking');
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Cycle Calendar',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Track your cycle patterns and predictions',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: Column(
        children: [
          // First row of tabs
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, '/cycle-tracking');
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      'Overview',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF7A5C),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Text(
                    'Calendar',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          // Second row of tabs
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, '/insights');
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      'Insights',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, '/daily-tracking');
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      'Daily Tracking',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_left, color: Colors.grey.shade600),
            onPressed: () {
              setState(() {
                currentMonth = DateTime(
                  currentMonth.year,
                  currentMonth.month - 1,
                  1,
                );
              });
            },
          ),
          Text(
            _getMonthYearString(currentMonth),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          IconButton(
            icon: Icon(Icons.chevron_right, color: Colors.grey.shade600),
            onPressed: () {
              setState(() {
                currentMonth = DateTime(
                  currentMonth.year,
                  currentMonth.month + 1,
                  1,
                );
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Day headers
          Row(
            children: const [
              DayHeader(label: "Sun"),
              DayHeader(label: "Mon"),
              DayHeader(label: "Tue"),
              DayHeader(label: "Wed"),
              DayHeader(label: "Thu"),
              DayHeader(label: "Fri"),
              DayHeader(label: "Sat"),
            ],
          ),
          const SizedBox(height: 10),
          // Calendar grid
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  Widget _buildLegendCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF7A5C).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Color(0xFFFF7A5C),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Calendar Legend',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: const [
              LegendItem(color: Color(0xFFFFE5E5), label: "Period Days"),
              SizedBox(width: 20),
              LegendItem(color: Color(0xFFE5F5E5), label: "Fertile Window"),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: const [
              LegendItem(color: Color(0xFFE5F0FF), label: "Ovulation Day"),
              SizedBox(width: 20),
              LegendItem(color: Color(0xFFFFF0E5), label: "Predicted Days"),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDayDetailsCard() {
    if (selectedDay == null) return const SizedBox.shrink();

    final note = dayNotes[selectedDay!];
    final type = cycleDays[selectedDay!] ?? CycleDayType.normal;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF7A5C).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.event_note,
                  color: Color(0xFFFF7A5C),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Day $selectedDay Details',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Type: ${_getCycleTypeLabel(type)}',
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          if (note != null) ...[
            const SizedBox(height: 8),
            Text(
              'Note: $note',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),
          ],
        ],
      ),
    );
  }

  String _getMonthYearString(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  String _getCycleTypeLabel(CycleDayType type) {
    switch (type) {
      case CycleDayType.period:
        return 'Period Day';
      case CycleDayType.ovulation:
        return 'Ovulation Day';
      case CycleDayType.fertile:
        return 'Fertile Window';
      case CycleDayType.predicted:
        return 'Predicted Period';
      case CycleDayType.today:
        return 'Today';
      case CycleDayType.normal:
        return 'Normal Day';
    }
  }

  Widget _buildCalendarGrid() {
    // Get first day of month to determine starting position
    int firstDayOfWeek =
        DateTime(currentMonth.year, currentMonth.month, 1).weekday;
    // Adjust for Sunday start (US calendar)
    firstDayOfWeek = firstDayOfWeek % 7;

    // Get number of days in month
    int daysInMonth =
        DateTime(currentMonth.year, currentMonth.month + 1, 0).day;

    List<Widget> calendarCells = [];

    // Add empty cells for days before the 1st of the month
    for (int i = 0; i < firstDayOfWeek; i++) {
      calendarCells.add(const EmptyCalendarCell());
    }

    // Add cells for each day of the month
    for (int day = 1; day <= daysInMonth; day++) {
      CycleDayType type = cycleDays[day] ?? CycleDayType.normal;
      calendarCells.add(
        CalendarDayCell(
          day: day,
          type: type,
          currentMonth: currentMonth,
          onTap: () {
            setState(() {
              selectedDay = day;
            });
          },
        ),
      );
    }

    // Create rows with 7 days each
    List<Widget> calendarRows = [];
    for (int i = 0; i < calendarCells.length; i += 7) {
      final List<Widget> rowChildren = [];
      for (int j = i; j < i + 7 && j < calendarCells.length; j++) {
        rowChildren.add(calendarCells[j]);
      }

      // If row is not complete, add empty cells
      if (rowChildren.length < 7) {
        int emptyToAdd = 7 - rowChildren.length;
        for (int k = 0; k < emptyToAdd; k++) {
          rowChildren.add(const EmptyCalendarCell());
        }
      }

      calendarRows.add(Row(children: rowChildren));
    }

    return Column(children: calendarRows);
  }
}

class DayHeader extends StatelessWidget {
  final String label;

  const DayHeader({Key? key, required this.label}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12),
          ),
        ),
      ),
    );
  }
}

class CalendarDayCell extends StatelessWidget {
  final int day;
  final CycleDayType type;
  final DateTime currentMonth;
  final VoidCallback? onTap;

  const CalendarDayCell({
    Key? key,
    required this.day,
    required this.type,
    required this.currentMonth,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor = Colors.black87;
    Color borderColor = Colors.grey.shade200;
    String? label;
    bool isToday = false;

    // Check if this is today
    final now = DateTime.now();
    if (currentMonth.year == now.year &&
        currentMonth.month == now.month &&
        day == now.day) {
      isToday = true;
    }

    switch (type) {
      case CycleDayType.period:
        backgroundColor = const Color(0xFFFFE5E5);
        borderColor = const Color(0xFFFF7A5C);
        label = "Period";
        break;
      case CycleDayType.ovulation:
        backgroundColor = const Color(0xFFE5F0FF);
        borderColor = const Color(0xFF4A90E2);
        label = "Ovulation";
        break;
      case CycleDayType.fertile:
        backgroundColor = const Color(0xFFE5F5E5);
        borderColor = const Color(0xFF4CAF50);
        label = "Fertile";
        break;
      case CycleDayType.predicted:
        backgroundColor = const Color(0xFFFFF0E5);
        borderColor = const Color(0xFFFF9800);
        label = "Predicted";
        break;
      case CycleDayType.today:
      case CycleDayType.normal:
        backgroundColor =
            isToday
                ? const Color(0xFFFF7A5C).withValues(alpha: 0.1)
                : Colors.white;
        borderColor = isToday ? const Color(0xFFFF7A5C) : Colors.grey.shade200;
        if (isToday) {
          textColor = const Color(0xFFFF7A5C);
          label = "Today";
        }
        break;
    }

    return Expanded(
      child: AspectRatio(
        aspectRatio: 1.0,
        child: GestureDetector(
          onTap: onTap,
          child: Container(
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: borderColor,
                width: isToday || type != CycleDayType.normal ? 2 : 1,
              ),
              boxShadow:
                  type != CycleDayType.normal
                      ? [
                        BoxShadow(
                          color: borderColor.withValues(alpha: 0.2),
                          spreadRadius: 0,
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ]
                      : null,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  day.toString(),
                  style: TextStyle(
                    fontWeight: isToday ? FontWeight.bold : FontWeight.w500,
                    fontSize: 16,
                    color: textColor,
                  ),
                ),
                if (label != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      label,
                      style: TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: borderColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class EmptyCalendarCell extends StatelessWidget {
  const EmptyCalendarCell({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: AspectRatio(
        aspectRatio: 1.0,
        child: Container(
          margin: const EdgeInsets.all(1),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey.shade200),
          ),
        ),
      ),
    );
  }
}

class LegendItem extends StatelessWidget {
  final Color color;
  final String label;

  const LegendItem({Key? key, required this.color, required this.label})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black87),
        ),
      ],
    );
  }
}
