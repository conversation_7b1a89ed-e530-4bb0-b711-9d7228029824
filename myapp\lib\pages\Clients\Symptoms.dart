import 'package:flutter/material.dart';

class SymptomsScreen extends StatefulWidget {
  const SymptomsScreen({super.key});

  @override
  State<SymptomsScreen> createState() => _SymptomsScreenState();
}

class _SymptomsScreenState extends State<SymptomsScreen> {
  int _selectedTab = 0;
  Map<String, int> _selectedSymptoms = {};
  DateTime _selectedDate = DateTime.now();

  final List<Map<String, dynamic>> _physicalSymptoms = [
    {'name': 'Cramps', 'icon': Icons.healing, 'color': Colors.red},
    {'name': 'Headache', 'icon': Icons.psychology, 'color': Colors.purple},
    {'name': 'Bloating', 'icon': Icons.circle, 'color': Colors.blue},
    {'name': 'Breast Tenderness', 'icon': Icons.favorite, 'color': Colors.pink},
    {
      'name': 'Back Pain',
      'icon': Icons.accessibility_new,
      'color': Colors.orange,
    },
    {'name': 'Nausea', 'icon': Icons.sick, 'color': Colors.green},
    {'name': 'Fatigue', 'icon': Icons.battery_0_bar, 'color': Colors.grey},
    {'name': 'Hot Flashes', 'icon': Icons.whatshot, 'color': Colors.deepOrange},
  ];

  final List<Map<String, dynamic>> _emotionalSymptoms = [
    {'name': 'Mood Swings', 'icon': Icons.mood, 'color': Colors.indigo},
    {'name': 'Anxiety', 'icon': Icons.psychology_alt, 'color': Colors.teal},
    {
      'name': 'Irritability',
      'icon': Icons.sentiment_dissatisfied,
      'color': Colors.red,
    },
    {
      'name': 'Depression',
      'icon': Icons.sentiment_very_dissatisfied,
      'color': Colors.blueGrey,
    },
    {'name': 'Stress', 'icon': Icons.warning, 'color': Colors.amber},
    {
      'name': 'Emotional',
      'icon': Icons.sentiment_neutral,
      'color': Colors.cyan,
    },
  ];

  final List<Map<String, dynamic>> _otherSymptoms = [
    {'name': 'Acne', 'icon': Icons.face, 'color': Colors.brown},
    {
      'name': 'Food Cravings',
      'icon': Icons.restaurant,
      'color': Colors.deepPurple,
    },
    {'name': 'Sleep Issues', 'icon': Icons.bedtime, 'color': Colors.indigo},
    {'name': 'Discharge', 'icon': Icons.water_drop, 'color': Colors.lightBlue},
    {'name': 'Spotting', 'icon': Icons.opacity, 'color': Colors.red[300]!},
    {
      'name': 'Weight Gain',
      'icon': Icons.monitor_weight,
      'color': Colors.orange,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: Column(
        children: [
          // Enhanced Header
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFFFF6A39), Color(0xFFFF8A50)],
              ),
            ),
            padding: EdgeInsets.fromLTRB(
              24,
              MediaQuery.of(context).padding.top + 20,
              24,
              24,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Back button
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Symptom Tracking',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Track and monitor your cycle symptoms',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),

          // Date Selector
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  color: Color(0xFFFF6A39),
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Today',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  'Day 14 of cycle',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(child: _buildTabButton('Physical', 0)),
                Expanded(child: _buildTabButton('Emotional', 1)),
                Expanded(child: _buildTabButton('Other', 2)),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildSymptomGrid(),
                  const SizedBox(height: 24),
                  _buildSymptomSummary(),
                  const SizedBox(height: 24),
                  _buildSymptomInsights(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(String title, int index) {
    final isSelected = _selectedTab == index;
    return GestureDetector(
      onTap: () => setState(() => _selectedTab = index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFF6A39) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[600],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildSymptomGrid() {
    List<Map<String, dynamic>> symptoms;
    switch (_selectedTab) {
      case 0:
        symptoms = _physicalSymptoms;
        break;
      case 1:
        symptoms = _emotionalSymptoms;
        break;
      case 2:
        symptoms = _otherSymptoms;
        break;
      default:
        symptoms = _physicalSymptoms;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: symptoms.length,
      itemBuilder: (context, index) {
        final symptom = symptoms[index];
        final isSelected = _selectedSymptoms.containsKey(symptom['name']);
        return _buildSymptomCard(symptom, isSelected);
      },
    );
  }

  Widget _buildSymptomCard(Map<String, dynamic> symptom, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedSymptoms.remove(symptom['name']);
          } else {
            _selectedSymptoms[symptom['name']] = 1;
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? symptom['color'].withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? symptom['color'] : Colors.grey.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              symptom['icon'],
              color: isSelected ? symptom['color'] : Colors.grey[600],
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              symptom['name'],
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? symptom['color'] : Colors.grey[700],
              ),
            ),
            if (isSelected) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildIntensityButton(symptom['name'], 1),
                  const SizedBox(width: 4),
                  _buildIntensityButton(symptom['name'], 2),
                  const SizedBox(width: 4),
                  _buildIntensityButton(symptom['name'], 3),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildIntensityButton(String symptomName, int intensity) {
    final isSelected = _selectedSymptoms[symptomName] == intensity;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedSymptoms[symptomName] = intensity;
        });
      },
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFF6A39) : Colors.grey[300],
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            intensity.toString(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSymptomSummary() {
    if (_selectedSymptoms.isEmpty) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(Icons.add_circle_outline, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No symptoms logged today',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Tap symptoms above to track them',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.summarize, color: Color(0xFFFF6A39), size: 24),
              const SizedBox(width: 12),
              const Text(
                'Today\'s Summary',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._selectedSymptoms.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Color(0xFFFF6A39),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      entry.key,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  _buildIntensityIndicator(entry.value),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildIntensityIndicator(int intensity) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.only(left: 2),
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color:
                index < intensity ? const Color(0xFFFF6A39) : Colors.grey[300],
            shape: BoxShape.circle,
          ),
        );
      }),
    );
  }

  Widget _buildSymptomInsights() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFFF8F0), Color(0xFFFFEFE5)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.insights, color: Color(0xFFFF6A39), size: 24),
              const SizedBox(width: 12),
              const Text(
                'Symptom Insights',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInsightItem(
            icon: Icons.trending_up,
            title: 'Pattern Recognition',
            description: 'Cramps typically occur 2-3 days before your period',
            color: Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildInsightItem(
            icon: Icons.psychology,
            title: 'Mood Tracking',
            description: 'Mood swings are most common during luteal phase',
            color: Colors.purple,
          ),
          const SizedBox(height: 12),
          _buildInsightItem(
            icon: Icons.lightbulb,
            title: 'Health Tip',
            description: 'Regular exercise can help reduce PMS symptoms',
            color: Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
