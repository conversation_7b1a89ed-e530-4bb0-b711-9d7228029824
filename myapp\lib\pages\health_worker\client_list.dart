import 'package:flutter/material.dart';
import 'Follow_ups.dart';
import 'Registration.dart';
import 'Reports.dart';
import 'trends.dart';

void main() => runApp(const ClientListApp());

class ClientListApp extends StatelessWidget {
  const ClientListApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Client List',
      theme: ThemeData(fontFamily: 'Arial'),
      home: const ClientListPage(),
    );
  }
}

class ClientListPage extends StatelessWidget {
  const ClientListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFDF2EC),
      appBar: AppBar(
        title: const Text('Client List', style: TextStyle(color: Colors.black)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.search, color: Colors.grey),
                        SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            decoration: InputDecoration(
                              hintText: 'Search clients...',
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: const Row(
                    children: [
                      Text("All Methods"),
                      Icon(Icons.keyboard_arrow_down),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: const Row(
                    children: [
                      Text("Sort by Name"),
                      Icon(Icons.keyboard_arrow_down),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView(
                children:
                    clients
                        .map((client) => ClientCard(client: client))
                        .toList(),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 1, // Clients tab is selected
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.orange[700],
        unselectedItemColor: Colors.grey[600],
        onTap: (index) {
          if (index == 0) {
            // Navigate to Home page
            Navigator.pop(context);
          } else if (index == 1) {
            // Already on Clients page
          } else if (index == 2) {
            // Navigate to Register page
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ClientRegistrationPage(),
              ),
            );
          } else if (index == 3) {
            // Navigate to Follow-ups page
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const FollowUpsPage()),
            );
          } else if (index == 4) {
            // Show a dialog to choose between Reports Dashboard and Trends
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('Select Report Type'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListTile(
                        leading: Icon(
                          Icons.dashboard,
                          color: Colors.orange[700],
                        ),
                        title: const Text('Reports Dashboard'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const DashboardScreen(),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: Icon(
                          Icons.trending_up,
                          color: Colors.orange[700],
                        ),
                        title: const Text('Trends'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const TrendsScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people_outline),
            label: 'Clients',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_add_outlined),
            label: 'Register',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today_outlined),
            label: 'Follow-ups',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart_outlined),
            label: 'Reports',
          ),
        ],
      ),
    );
  }
}

class ClientCard extends StatelessWidget {
  final Map<String, String> client;
  const ClientCard({super.key, required this.client});

  @override
  Widget build(BuildContext context) {
    final bool isOverdue = client['status'] == 'Follow-up Overdue';
    final bool isUpToDate = client['status'] == 'Up to Date';

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  client['name']!,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isOverdue
                            ? const Color(0xFFFFE5E5)
                            : isUpToDate
                            ? const Color(0xFFE5F9EB)
                            : Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    client['status']!,
                    style: TextStyle(
                      fontSize: 12,
                      color:
                          isOverdue
                              ? Colors.red
                              : isUpToDate
                              ? Colors.green
                              : Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text("Age: ${client['age']}", style: const TextStyle(fontSize: 13)),
            Text(
              "Method: ${client['method']}",
              style: const TextStyle(fontSize: 13),
            ),
            Text(
              "Last Visit: ${client['lastVisit']}",
              style: const TextStyle(fontSize: 13),
            ),
            Text(
              "Next Follow-up: ${client['nextFollowUp']}",
              style: const TextStyle(fontSize: 13),
            ),
            Text("ID: ${client['id']}", style: const TextStyle(fontSize: 13)),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () {},
                child: const Text(
                  'View Details →',
                  style: TextStyle(color: Colors.deepOrangeAccent),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

final List<Map<String, String>> clients = [
  {
    'name': 'Ana Martinez',
    'age': '34',
    'method': 'IUD',
    'lastVisit': 'Apr 22, 2023',
    'nextFollowUp': 'Jul 22, 2023',
    'id': '2',
    'status': 'Follow-up Overdue',
  },
  {
    'name': 'Carlos Sanchez',
    'age': '28',
    'method': 'Vasectomy',
    'lastVisit': 'Mar 5, 2023',
    'nextFollowUp': 'N/A',
    'id': '5',
    'status': 'Up to Date',
  },
  {
    'name': 'Elena Rodriguez',
    'age': '29',
    'method': 'Injectable',
    'lastVisit': 'May 10, 2023',
    'nextFollowUp': 'Jun 10, 2023',
    'id': '4',
    'status': 'Follow-up Overdue',
  },
  {
    'name': 'John Doe',
    'age': '31',
    'method': 'Condoms',
    'lastVisit': 'May 18, 2023',
    'nextFollowUp': 'Jun 18, 2023',
    'id': '3',
    'status': 'Follow-up Overdue',
  },
  {
    'name': 'Maria Garcia',
    'age': '28',
    'method': 'Oral contraceptive',
    'lastVisit': 'May 15, 2023',
    'nextFollowUp': 'Jun 15, 2023',
    'id': '1',
    'status': 'Follow-up Overdue',
  },
  {
    'name': 'Sofia Reyes',
    'age': '22',
    'method': 'Implant',
    'lastVisit': 'May 1, 2023',
    'nextFollowUp': 'May 1, 2026',
    'id': '6',
    'status': 'Up to Date',
  },
];
