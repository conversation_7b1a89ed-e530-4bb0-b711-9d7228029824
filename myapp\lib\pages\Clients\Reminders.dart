import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class RemindersScreen extends StatefulWidget {
  const RemindersScreen({super.key});

  @override
  State<RemindersScreen> createState() => _RemindersScreenState();
}

class _RemindersScreenState extends State<RemindersScreen> {
  int _selectedTab = 0;

  // Reminder categories
  final List<Map<String, dynamic>> _cycleReminders = [
    {
      'id': 'period_start',
      'title': 'Period Start',
      'description': '2 days before expected date',
      'icon': Icons.water_drop,
      'color': Colors.red,
      'time': '09:00 AM',
      'enabled': true,
      'frequency': 'Cycle-based',
    },
    {
      'id': 'ovulation',
      'title': 'Ovulation Window',
      'description': '3 days before ovulation',
      'icon': Icons.favorite,
      'color': Colors.pink,
      'time': '08:00 AM',
      'enabled': true,
      'frequency': 'Cycle-based',
    },
    {
      'id': 'fertile_window',
      'title': 'Fertile Window',
      'description': 'Start of fertile period',
      'icon': Icons.child_care,
      'color': Colors.green,
      'time': '07:30 AM',
      'enabled': false,
      'frequency': 'Cycle-based',
    },
    {
      'id': 'pms_symptoms',
      'title': 'PMS Symptoms',
      'description': '5 days before period',
      'icon': Icons.mood_bad,
      'color': Colors.orange,
      'time': '06:00 PM',
      'enabled': true,
      'frequency': 'Cycle-based',
    },
  ];

  final List<Map<String, dynamic>> _dailyReminders = [
    {
      'id': 'log_symptoms',
      'title': 'Log Symptoms',
      'description': 'Track daily symptoms',
      'icon': Icons.edit_note,
      'color': Colors.blue,
      'time': '08:00 PM',
      'enabled': false,
      'frequency': 'Daily',
    },
    {
      'id': 'take_temperature',
      'title': 'Take Temperature',
      'description': 'Morning basal temperature',
      'icon': Icons.thermostat,
      'color': Colors.purple,
      'time': '07:00 AM',
      'enabled': true,
      'frequency': 'Daily',
    },
    {
      'id': 'drink_water',
      'title': 'Drink Water',
      'description': 'Stay hydrated',
      'icon': Icons.local_drink,
      'color': Colors.cyan,
      'time': '10:00 AM',
      'enabled': true,
      'frequency': 'Daily',
    },
    {
      'id': 'take_vitamins',
      'title': 'Take Vitamins',
      'description': 'Daily supplements',
      'icon': Icons.medication,
      'color': Colors.teal,
      'time': '09:00 AM',
      'enabled': false,
      'frequency': 'Daily',
    },
  ];

  final List<Map<String, dynamic>> _medicationReminders = [
    {
      'id': 'birth_control',
      'title': 'Birth Control Pill',
      'description': 'Daily contraceptive',
      'icon': Icons.medical_services,
      'color': Colors.indigo,
      'time': '09:00 PM',
      'enabled': true,
      'frequency': 'Daily',
    },
    {
      'id': 'iron_supplement',
      'title': 'Iron Supplement',
      'description': 'During menstruation',
      'icon': Icons.healing,
      'color': Colors.brown,
      'time': '08:00 AM',
      'enabled': false,
      'frequency': 'Period only',
    },
    {
      'id': 'pain_relief',
      'title': 'Pain Relief',
      'description': 'For cramps and discomfort',
      'icon': Icons.local_pharmacy,
      'color': Colors.red[700]!,
      'time': 'As needed',
      'enabled': false,
      'frequency': 'As needed',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: Column(
        children: [
          // Enhanced Header
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFFFF6A39), Color(0xFFFF8A50)],
              ),
            ),
            padding: EdgeInsets.fromLTRB(
              24,
              MediaQuery.of(context).padding.top + 20,
              24,
              24,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Back button
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Smart Reminders',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Never miss important cycle events',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),

          // Quick Stats
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.notifications_active,
                    title: 'Active',
                    value: '6',
                    color: Colors.green,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey[300]),
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.schedule,
                    title: 'Upcoming',
                    value: '3',
                    color: Colors.orange,
                  ),
                ),
                Container(width: 1, height: 40, color: Colors.grey[300]),
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.notifications_off,
                    title: 'Inactive',
                    value: '5',
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // Tab Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(child: _buildTabButton('Cycle', 0)),
                Expanded(child: _buildTabButton('Daily', 1)),
                Expanded(child: _buildTabButton('Medication', 2)),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildRemindersList(),
                  const SizedBox(height: 20),
                  _buildAddReminderButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(title, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Widget _buildTabButton(String title, int index) {
    final isSelected = _selectedTab == index;
    return GestureDetector(
      onTap: () => setState(() => _selectedTab = index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFF6A39) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[600],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildRemindersList() {
    List<Map<String, dynamic>> reminders;
    switch (_selectedTab) {
      case 0:
        reminders = _cycleReminders;
        break;
      case 1:
        reminders = _dailyReminders;
        break;
      case 2:
        reminders = _medicationReminders;
        break;
      default:
        reminders = _cycleReminders;
    }

    return Column(
      children:
          reminders.map((reminder) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildEnhancedReminderCard(reminder),
            );
          }).toList(),
    );
  }

  Widget _buildEnhancedReminderCard(Map<String, dynamic> reminder) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              reminder['enabled']
                  ? reminder['color'].withOpacity(0.3)
                  : Colors.grey.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Icon
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: reminder['color'].withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  reminder['icon'],
                  color: reminder['color'],
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      reminder['title'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      reminder['description'],
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),

              // Toggle switch
              CupertinoSwitch(
                value: reminder['enabled'],
                onChanged: (value) {
                  setState(() {
                    reminder['enabled'] = value;
                  });
                },
                activeTrackColor: const Color(0xFFFF6A39),
              ),
            ],
          ),

          if (reminder['enabled']) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: reminder['color'].withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.schedule, color: reminder['color'], size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'Time: ${reminder['time']}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: reminder['color'],
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: reminder['color'].withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      reminder['frequency'],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: reminder['color'],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddReminderButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFFF8F0), Color(0xFFFFEFE5)],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFF6A39).withOpacity(0.2),
          width: 2,
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFFF6A39).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(Icons.add, color: Color(0xFFFF6A39), size: 32),
          ),
          const SizedBox(height: 16),
          const Text(
            'Create Custom Reminder',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Set up personalized reminders for your cycle',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              _showAddReminderDialog();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF6A39),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 0,
            ),
            child: const Text(
              'Add Reminder',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddReminderDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Add Custom Reminder',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Custom reminder creation will be available in the next update.',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6A39).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.lightbulb,
                      color: Color(0xFFFF6A39),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'For now, you can customize existing reminders by toggling them on/off.',
                        style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Got it',
                style: TextStyle(
                  color: Color(0xFFFF6A39),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
