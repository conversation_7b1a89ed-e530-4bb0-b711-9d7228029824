import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        scaffoldBackgroundColor: Colors.white,
      ),
      home: const DietPreferenceScreen(),
    );
  }
}

class DietPreferenceScreen extends StatefulWidget {
  const DietPreferenceScreen({Key? key}) : super(key: key);

  @override
  _DietPreferenceScreenState createState() => _DietPreferenceScreenState();
}

class _DietPreferenceScreenState extends State<DietPreferenceScreen> {
  // Enhanced diet preferences with detailed information
  Map<String, Map<String, dynamic>> dietPreferences = {
    'Regular': {
      'selected': false,
      'icon': Icons.restaurant,
      'description': 'Balanced diet with all food groups',
      'color': Colors.blue,
      'benefits': ['Balanced nutrition', 'Easy to follow', 'Flexible options'],
      'category': 'General',
    },
    'Vegetarian': {
      'selected': false,
      'icon': Icons.eco,
      'description': 'Plant-based diet excluding meat and fish',
      'color': Colors.green,
      'benefits': [
        'Heart health',
        'Environmental friendly',
        'Lower cholesterol',
      ],
      'category': 'Lifestyle',
    },
    'Vegan': {
      'selected': false,
      'icon': Icons.local_florist,
      'description': 'Plant-based diet excluding all animal products',
      'color': Colors.lightGreen,
      'benefits': [
        'Weight management',
        'Reduced inflammation',
        'Ethical choice',
      ],
      'category': 'Lifestyle',
    },
    'Keto': {
      'selected': false,
      'icon': Icons.fitness_center,
      'description': 'High-fat, low-carb diet for weight loss',
      'color': Colors.purple,
      'benefits': [
        'Rapid weight loss',
        'Mental clarity',
        'Blood sugar control',
      ],
      'category': 'Weight Loss',
    },
    'Mediterranean': {
      'selected': false,
      'icon': Icons.waves,
      'description': 'Heart-healthy diet rich in olive oil and fish',
      'color': Colors.blue,
      'benefits': ['Heart health', 'Brain function', 'Longevity'],
      'category': 'Health',
    },
    'Low-Carb': {
      'selected': false,
      'icon': Icons.remove_circle,
      'description': 'Reduced carbohydrate intake for weight control',
      'color': Colors.orange,
      'benefits': ['Weight loss', 'Blood sugar control', 'Reduced appetite'],
      'category': 'Weight Loss',
    },
    'Gluten-Free': {
      'selected': false,
      'icon': Icons.no_food,
      'description': 'Eliminates gluten-containing grains',
      'color': Colors.amber,
      'benefits': ['Digestive health', 'Reduced inflammation', 'Energy boost'],
      'category': 'Health',
    },
    'Intermittent Fasting': {
      'selected': false,
      'icon': Icons.schedule,
      'description': 'Time-restricted eating patterns',
      'color': Colors.indigo,
      'benefits': ['Weight management', 'Cellular repair', 'Mental clarity'],
      'category': 'Weight Loss',
    },
  };

  String selectedCategory = 'All';
  List<String> categories = [
    'All',
    'General',
    'Weight Loss',
    'Health',
    'Lifestyle',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildCategoryFilter(),
                  const SizedBox(height: 20),
                  _buildDietGrid(),
                  const SizedBox(height: 20),
                  _buildSelectedSummary(),
                  const SizedBox(height: 20),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF7A5C), Color(0xFFFF6B47)],
        ),
      ),
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16,
        left: 20,
        right: 20,
        bottom: 24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button and progress
          Row(
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.arrow_back, color: Colors.white, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Back',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Step 2 of 3',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            'Diet Preferences',
            style: TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose your preferred diet types to get personalized nutrition recommendations that fit your lifestyle.',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.category, color: Color(0xFFFF7A5C), size: 24),
              SizedBox(width: 12),
              Text(
                'Filter by Category',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  categories.map((category) {
                    final isSelected = selectedCategory == category;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedCategory = category;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? const Color(0xFFFF7A5C)
                                    : Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? const Color(0xFFFF7A5C)
                                      : Colors.grey.shade300,
                            ),
                          ),
                          child: Text(
                            category,
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? Colors.white
                                      : Colors.grey.shade700,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDietGrid() {
    final filteredDiets =
        dietPreferences.entries.where((entry) {
          if (selectedCategory == 'All') return true;
          return entry.value['category'] == selectedCategory;
        }).toList();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.restaurant_menu, color: Color(0xFFFF7A5C), size: 24),
              SizedBox(width: 12),
              Text(
                'Select Your Diet Preferences',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.85,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: filteredDiets.length,
            itemBuilder: (context, index) {
              final entry = filteredDiets[index];
              final dietName = entry.key;
              final dietData = entry.value;
              final isSelected = dietData['selected'] as bool;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    dietPreferences[dietName]!['selected'] = !isSelected;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? const Color(0xFFFFF0E6)
                            : Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFFFF7A5C)
                              : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            dietData['icon'] as IconData,
                            size: 28,
                            color:
                                isSelected
                                    ? const Color(0xFFFF7A5C)
                                    : (dietData['color'] as Color),
                          ),
                          const Spacer(),
                          if (isSelected)
                            const Icon(
                              Icons.check_circle,
                              color: Color(0xFFFF7A5C),
                              size: 20,
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        dietName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              isSelected
                                  ? const Color(0xFFFF7A5C)
                                  : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        dietData['description'] as String,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Benefits:',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            ...((dietData['benefits'] as List<String>)
                                .take(2)
                                .map(
                                  (benefit) => Padding(
                                    padding: const EdgeInsets.only(bottom: 2),
                                    child: Text(
                                      '• $benefit',
                                      style: TextStyle(
                                        fontSize: 9,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedSummary() {
    final selectedDiets =
        dietPreferences.entries
            .where((entry) => entry.value['selected'] as bool)
            .map((entry) => entry.key)
            .toList();

    if (selectedDiets.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: const Row(
          children: [
            Icon(Icons.info_outline, color: Colors.blue, size: 24),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'No diet preferences selected. You can proceed with general nutrition recommendations.',
                style: TextStyle(color: Colors.blue, fontSize: 14),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.checklist, color: Color(0xFFFF7A5C), size: 24),
              SizedBox(width: 12),
              Text(
                'Selected Diet Preferences',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                selectedDiets.map((diet) {
                  final dietData = dietPreferences[diet]!;
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF7A5C),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          dietData['icon'] as IconData,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          diet,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              dietPreferences[diet]!['selected'] = false;
                            });
                          },
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: () {
              _saveDietPreferencesAndProceed();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF7A5C),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 2,
            ),
            child: const Text(
              'Continue to Nutrition Chat',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: () {
              _clearAllSelections();
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFFFF7A5C),
              side: const BorderSide(color: Color(0xFFFF7A5C)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text(
              'Clear All Selections',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ],
    );
  }

  void _saveDietPreferencesAndProceed() {
    final selectedDiets =
        dietPreferences.entries
            .where((entry) => entry.value['selected'] as bool)
            .map((entry) => entry.key)
            .toList();

    // Show confirmation snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          selectedDiets.isEmpty
              ? 'No diet preferences selected. Proceeding with general recommendations.'
              : 'Recorded ${selectedDiets.length} diet preferences. Proceeding to nutrition chat.',
        ),
        backgroundColor: const Color(0xFFFF7A5C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );

    // Navigate to nutrition chat
    Navigator.pushNamed(context, '/nutritious-msg');
  }

  void _clearAllSelections() {
    setState(() {
      for (var key in dietPreferences.keys) {
        dietPreferences[key]!['selected'] = false;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All selections cleared.'),
        backgroundColor: Color(0xFFFF7A5C),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
