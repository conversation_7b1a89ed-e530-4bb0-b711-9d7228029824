import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class Analytics extends StatelessWidget {
  const Analytics({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Analytics & Reports',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.deepOrange,
        scaffoldBackgroundColor: const Color(0xFFF9FAFB),
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.black),
          titleTextStyle: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      home: const AnalyticsReportsScreen(),
    );
  }
}

class AnalyticsReportsScreen extends StatefulWidget {
  const AnalyticsReportsScreen({Key? key}) : super(key: key);

  @override
  _AnalyticsReportsScreenState createState() => _AnalyticsReportsScreenState();
}

class _AnalyticsReportsScreenState extends State<AnalyticsReportsScreen> {
  String _selectedTimeFrame = 'This Month';
  final List<String> _timeFrames = [
    'This Week',
    'This Month',
    'This Quarter',
    'This Year',
  ];

  final Map<String, dynamic> _metrics = {
    'Total Users': {
      'value': '3,842',
      'change': '+12% from last month',
      'isPositive': true,
    },
    'Active Sessions': {
      'value': '1,248',
      'change': '+5% from last month',
      'isPositive': true,
    },
    'Educational Content': {
      'value': '12.5k',
      'change': '-21% from last month',
      'isPositive': false,
    },
    'Voice Feature Usage': {
      'value': '5,621',
      'change': '+16% from last month',
      'isPositive': true,
    },
    'Online Usage': {
      'value': '2,891',
      'change': '+18% from last month',
      'isPositive': true,
    },
    'Community Events': {
      'value': '156',
      'change': '+32% from last month',
      'isPositive': true,
    },
    'Resource Downloads': {
      'value': '8.2k',
      'change': '+15% from last month',
      'isPositive': true,
    },
    'Language Support': {
      'value': '12',
      'change': '+2 new languages',
      'isPositive': true,
    },
  };

  final List<Map<String, dynamic>> _reports = [
    {
      'title': 'User Demographics Report',
      'description': 'Age, location, and usage patterns',
      'icon': Icons.person,
      'color': Colors.orange[100],
      'iconColor': Colors.orange[700],
    },
    {
      'title': 'Community Engagement',
      'description': 'User interaction and content engagement',
      'icon': Icons.people,
      'color': Colors.green[100],
      'iconColor': Colors.green[700],
    },
    {
      'title': 'Quarterly Program Impact',
      'description': 'Milestones and outcomes analysis',
      'icon': Icons.assessment,
      'color': Colors.purple[100],
      'iconColor': Colors.purple[700],
    },
  ];

  // Sample data for the chart
  final List<FlSpot> _usageData = [
    FlSpot(0, 2.5),
    FlSpot(1, 2.0),
    FlSpot(2, 3.0),
    FlSpot(3, 2.5),
    FlSpot(4, 4.5),
    FlSpot(5, 4.0),
    FlSpot(6, 5.0),
  ];

  void _generateCustomReport() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Generate Custom Report'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Select metrics to include in your custom report:'),
                const SizedBox(height: 16),
                ...[
                  'User Growth',
                  'Engagement Metrics',
                  'Content Analytics',
                  'Feature Usage',
                ].map(
                  (metric) => CheckboxListTile(
                    title: Text(metric),
                    value: true,
                    onChanged: (value) {},
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Custom report generation would be implemented here',
                      ),
                    ),
                  );
                },
                child: const Text('Generate'),
              ),
            ],
          ),
    );
  }

  void _downloadReport(Map<String, dynamic> report) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Downloading ${report['title']}...')),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Filter Options',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text('Date Range'),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children:
                      [
                            'Last 7 days',
                            'Last 30 days',
                            'Last 3 months',
                            'Last 6 months',
                            'Custom range',
                          ]
                          .map(
                            (range) => ChoiceChip(
                              label: Text(range),
                              selected: range == 'Last 30 days',
                              onSelected: (selected) => Navigator.pop(context),
                            ),
                          )
                          .toList(),
                ),
                const SizedBox(height: 16),
                const Text('User Segments'),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children:
                      [
                            'All users',
                            'New users',
                            'Active users',
                            'Inactive users',
                          ]
                          .map(
                            (segment) => FilterChip(
                              label: Text(segment),
                              selected: segment == 'All users',
                              onSelected: (selected) {},
                            ),
                          )
                          .toList(),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Apply Filters'),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  void _viewDetailedAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Detailed analytics view would be displayed here'),
      ),
    );
  }

  // Build bottom navigation bar
  Widget _buildBottomNavigation() {
    return Builder(
      builder: (BuildContext context) {
        return BottomAppBar(
          color: Colors.white,
          elevation: 4,
          child: Container(
            height: 56,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(context, Icons.home, 'Home'),
                _buildNavItem(context, Icons.people, 'Users'),
                _buildNavItem(context, Icons.security, 'Security'),
                _buildNavItem(context, Icons.sync, 'Updates'),
                _buildNavItem(
                  context,
                  Icons.bar_chart,
                  'Reports',
                  isActive: true,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Build navigation item
  Widget _buildNavItem(
    BuildContext context,
    IconData icon,
    String label, {
    bool isActive = false,
  }) {
    Color color = isActive ? Color(0xFFFF6B35) : Color(0xFF888888);
    return Expanded(
      child: InkWell(
        onTap: () {
          if (label == 'Home') {
            Navigator.pushNamed(context, '/admin-dashboard');
          } else if (label == 'Users') {
            Navigator.pushNamed(context, '/user-management');
          } else if (label == 'Security') {
            Navigator.pushNamed(context, '/data-security');
          } else if (label == 'Updates') {
            Navigator.pushNamed(context, '/system-updates');
          }
          // No action for Reports since we're already on that page
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pushNamed(context, '/admin-dashboard'),
        ),
        title: const Text(
          'Analytics & Reports',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16.0),
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Program evaluation and insights',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Key Metrics',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.filter_list),
                                onPressed: _showFilterOptions,
                                tooltip: 'Filter',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                              ),
                              const SizedBox(width: 16),
                              DropdownButton<String>(
                                value: _selectedTimeFrame,
                                icon: const Icon(Icons.arrow_drop_down),
                                underline: const SizedBox(),
                                items:
                                    _timeFrames.map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      _selectedTimeFrame = newValue;
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 1.5,
                          crossAxisSpacing: 12,
                          mainAxisSpacing: 12,
                        ),
                    itemCount: _metrics.length,
                    itemBuilder: (context, index) {
                      final entry = _metrics.entries.elementAt(index);
                      return _buildMetricCard(
                        title: entry.key,
                        value: entry.value['value'],
                        change: entry.value['change'],
                        isPositive: entry.value['isPositive'],
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Usage Trends',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 200,
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withAlpha(26), // 0.1 * 255 ≈ 26
                          spreadRadius: 1,
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'System Usage Analytics',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            TextButton.icon(
                              onPressed: _viewDetailedAnalytics,
                              icon: const Icon(
                                Icons.trending_up,
                                size: 16,
                                color: Colors.deepOrange,
                              ),
                              label: const Text(
                                'View Details',
                                style: TextStyle(
                                  color: Colors.deepOrange,
                                  fontSize: 12,
                                ),
                              ),
                              style: TextButton.styleFrom(
                                padding: EdgeInsets.zero,
                                minimumSize: Size.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                            ),
                          ],
                        ),
                        const Text(
                          'Last 7 months performance',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        const SizedBox(height: 8),
                        Expanded(
                          child: LineChart(
                            LineChartData(
                              gridData: FlGridData(show: false),
                              titlesData: FlTitlesData(
                                leftTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(
                                    showTitles: true,
                                    reservedSize: 22,
                                    getTitlesWidget: (value, meta) {
                                      const monthNames = [
                                        '',
                                        'Jan',
                                        'Feb',
                                        'Mar',
                                        'Apr',
                                        'May',
                                        'Jun',
                                        'Jul',
                                      ];
                                      if (value.toInt() % 1 == 0 &&
                                          value.toInt() >= 0 &&
                                          value.toInt() < monthNames.length) {
                                        return Text(
                                          monthNames[value.toInt()],
                                          style: const TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey,
                                          ),
                                        );
                                      }
                                      return const Text('');
                                    },
                                  ),
                                ),
                                rightTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                topTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                              ),
                              borderData: FlBorderData(show: false),
                              minX: 0,
                              maxX: 6,
                              minY: 0,
                              maxY: 6,
                              lineBarsData: [
                                LineChartBarData(
                                  spots: _usageData,
                                  isCurved: true,
                                  color: Colors.deepOrange,
                                  barWidth: 3,
                                  isStrokeCapRound: true,
                                  dotData: FlDotData(show: false),
                                  belowBarData: BarAreaData(
                                    show: true,
                                    color: Colors.deepOrange.withAlpha(
                                      26,
                                    ), // 0.1 * 255 ≈ 26
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Available Reports',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ..._reports.map((report) => _buildReportCard(report)),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _generateCustomReport,
                    icon: const Icon(Icons.add_chart),
                    label: const Text('Generate Custom Report'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.deepOrange,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 48),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required String change,
    required bool isPositive,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 ≈ 26
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(color: Colors.grey, fontSize: 12)),
          Text(
            value,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Row(
            children: [
              Icon(
                isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                color: isPositive ? Colors.green : Colors.red,
                size: 14,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  change,
                  style: TextStyle(
                    fontSize: 12,
                    color: isPositive ? Colors.green : Colors.red,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(Map<String, dynamic> report) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26), // 0.1 * 255 ≈ 26
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: report['color'] as Color,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            report['icon'] as IconData,
            color: report['iconColor'] as Color,
            size: 20,
          ),
        ),
        title: Text(
          report['title'] as String,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(
          report['description'] as String,
          style: const TextStyle(fontSize: 12),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.download, color: Colors.grey),
          onPressed: () => _downloadReport(report),
        ),
        onTap: () => _downloadReport(report),
      ),
    );
  }

  // Removed duplicate _buildNavItem method
}
