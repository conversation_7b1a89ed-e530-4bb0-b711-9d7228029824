import 'package:flutter/material.dart';
import '../services/auth_service.dart';

class AuthProvider extends InheritedNotifier<AuthService> {
  const AuthProvider({
    Key? key,
    required AuthService authService,
    required Widget child,
  }) : super(key: key, notifier: authService, child: child);

  static AuthService of(BuildContext context) {
    final AuthProvider? provider = 
        context.dependOnInheritedWidgetOfExactType<AuthProvider>();
    if (provider == null) {
      throw Exception('AuthProvider not found in context');
    }
    return provider.notifier!;
  }
}
