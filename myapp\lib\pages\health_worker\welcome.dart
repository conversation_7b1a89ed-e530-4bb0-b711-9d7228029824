import 'package:flutter/material.dart';
import 'client_list.dart';
import 'Registration.dart';
import 'Follow_ups.dart';
import 'Reports.dart';
import 'trends.dart';

class HealthWorkerDashboard extends StatelessWidget {
  const HealthWorkerDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                // Welcome Banner with Notification
                const WelcomeBanner(),
                const SizedBox(height: 16),
                // Main Action Cards
                const MainActionCards(),
                const SizedBox(height: 16),
                // Second Row Action Cards
                const SecondRowActionCards(),
                const SizedBox(height: 24),
                // Educational Resources
                const EducationalResources(),
                const SizedBox(height: 24),
                // Quick Actions
                const QuickActions(),
                const SizedBox(height: 24),
                // Recent Activity
                const RecentActivity(),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: const CHWBottomNavigationBar(),
    );
  }
}

class WelcomeBanner extends StatelessWidget {
  const WelcomeBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(
              26,
            ), // Using withAlpha instead of withOpacity
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Welcome, Sarah',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red[700],
                ),
              ),
            ],
          ),
          const Text(
            'Community Health Worker',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.notifications_outlined,
                  color: Colors.orange[700],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'You have 9 follow-ups scheduled for today',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class MainActionCards extends StatelessWidget {
  const MainActionCards({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Manage Clients
        Expanded(
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const ClientListPage()),
              );
            },
            child: Card(
              elevation: 0,
              color: Colors.blue[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.people_outline,
                      size: 32,
                      color: Colors.blue[700],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Manage Clients',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Register Client
        Expanded(
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ClientRegistrationPage(),
                ),
              );
            },
            child: Card(
              elevation: 0,
              color: Colors.green[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_add_outlined,
                      size: 32,
                      color: Colors.green[700],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Register Client',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class ActionCard extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final Color backgroundColor;
  final String title;

  const ActionCard({
    super.key,
    required this.icon,
    required this.iconColor,
    required this.backgroundColor,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      color: backgroundColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: iconColor),
            const SizedBox(height: 8),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }
}

class SecondRowActionCards extends StatelessWidget {
  const SecondRowActionCards({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Follow-ups
        Expanded(
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const FollowUpsPage()),
              );
            },
            child: Card(
              elevation: 0,
              color: Colors.purple[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.event_note_outlined,
                      size: 32,
                      color: Colors.purple[700],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Follow-ups',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Reports
        Expanded(
          child: InkWell(
            onTap: () {
              // Show a dialog to choose between Reports Dashboard and Trends
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text('Select Report Type'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          leading: Icon(
                            Icons.dashboard,
                            color: Colors.orange[700],
                          ),
                          title: const Text('Reports Dashboard'),
                          onTap: () {
                            Navigator.pop(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const DashboardScreen(),
                              ),
                            );
                          },
                        ),
                        ListTile(
                          leading: Icon(
                            Icons.trending_up,
                            color: Colors.blue[700],
                          ),
                          title: const Text('Trends'),
                          onTap: () {
                            Navigator.pop(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const TrendsScreen(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  );
                },
              );
            },
            child: Card(
              elevation: 0,
              color: Colors.orange[50],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart_outlined,
                      size: 32,
                      color: Colors.orange[700],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Reports',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class EducationalResources extends StatelessWidget {
  const EducationalResources({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(
              26,
            ), // Using withAlpha instead of withOpacity
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Educational Resources',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          // Family Planning Guide
          ResourceItem(
            color: Colors.orange,
            title: 'Family Planning Guide',
            description:
                'Comprehensive guide to contraception methods and counseling',
            icon: Icons.menu_book_outlined,
            actionText: 'View Guide',
          ),
          const SizedBox(height: 16),
          // Counseling Techniques
          ResourceItem(
            color: Colors.purple,
            title: 'Counseling Techniques',
            description: 'Best practices for client communication and support',
            icon: Icons.psychology_outlined,
            actionText: 'Learn More',
          ),
          const SizedBox(height: 16),
          // Health Guidelines
          ResourceItem(
            color: Colors.green,
            title: 'Health Guidelines',
            description: 'Updated medical guidelines and protocols',
            icon: Icons.health_and_safety_outlined,
            actionText: 'View Guidelines',
          ),
        ],
      ),
    );
  }
}

class ResourceItem extends StatelessWidget {
  final MaterialColor color;
  final String title;
  final String description;
  final IconData icon;
  final String actionText;

  const ResourceItem({
    super.key,
    required this.color,
    required this.title,
    required this.description,
    required this.icon,
    required this.actionText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 4,
          height: 50,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(fontSize: 13, color: Colors.grey[600]),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(icon, size: 14, color: color),
                  const SizedBox(width: 4),
                  Text(
                    actionText,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: color,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: QuickActionItem(
                icon: Icons.phone_outlined,
                title: 'Emergency Contact',
                color: Colors.red[100]!,
                iconColor: Colors.red[700]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickActionItem(
                icon: Icons.notifications_outlined,
                title: 'Send Reminders',
                color: Colors.purple[100]!,
                iconColor: Colors.purple[700]!,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: QuickActionItem(
                icon: Icons.note_outlined,
                title: 'Daily Report',
                color: Colors.teal[100]!,
                iconColor: Colors.teal[700]!,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickActionItem(
                icon: Icons.calendar_today,
                title: 'Schedule Visit',
                color: Colors.blue[100]!,
                iconColor: Colors.blue[700]!,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class QuickActionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final Color color;
  final Color iconColor;

  const QuickActionItem({
    super.key,
    required this.icon,
    required this.title,
    required this.color,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: iconColor, size: 20),
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                color: Colors.grey[800],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RecentActivity extends StatelessWidget {
  const RecentActivity({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(
              26,
            ), // Using withAlpha instead of withOpacity
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Recent Activity',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          // Activity 1
          ActivityItem(
            color: Colors.blue,
            title: 'Maria Garcia completed her contraceptive consultation',
            time: 'Today, 10:30 AM',
          ),
          const SizedBox(height: 16),
          // Activity 2
          ActivityItem(
            color: Colors.green,
            title: 'New client John Doe registered',
            time: 'Yesterday, 3:15 PM',
          ),
          const SizedBox(height: 16),
          // Activity 3
          ActivityItem(
            color: Colors.orange,
            title: 'Follow-up reminder: Ana Martinez due for check-in',
            time: 'Yesterday, 9:45 AM',
          ),
        ],
      ),
    );
  }
}

class ActivityItem extends StatelessWidget {
  final MaterialColor color;
  final String title;
  final String time;

  const ActivityItem({
    super.key,
    required this.color,
    required this.title,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                time,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class CHWBottomNavigationBar extends StatelessWidget {
  const CHWBottomNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: 0,
      selectedItemColor: Colors.orange[700],
      unselectedItemColor: Colors.grey[600],
      onTap: (index) {
        if (index == 0) {
          // Already on Home page
        } else if (index == 1) {
          // Navigate to Clients page
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ClientListPage()),
          );
        } else if (index == 2) {
          // Navigate to Register page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ClientRegistrationPage(),
            ),
          );
        } else if (index == 3) {
          // Navigate to Follow-ups page
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const FollowUpsPage()),
          );
        } else if (index == 4) {
          // Show a dialog to choose between Reports Dashboard and Trends
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('Select Report Type'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: Icon(Icons.dashboard, color: Colors.orange[700]),
                      title: const Text('Reports Dashboard'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DashboardScreen(),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: Icon(Icons.trending_up, color: Colors.blue[700]),
                      title: const Text('Trends'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TrendsScreen(),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              );
            },
          );
        }
      },
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home_outlined),
          activeIcon: Icon(Icons.home),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.people_outline),
          activeIcon: Icon(Icons.people),
          label: 'Clients',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.calendar_today_outlined),
          activeIcon: Icon(Icons.calendar_today),
          label: 'Register',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.event_note_outlined),
          activeIcon: Icon(Icons.event_note),
          label: 'Follow-ups',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.bar_chart_outlined),
          activeIcon: Icon(Icons.bar_chart),
          label: 'Reports',
        ),
      ],
    );
  }
}
