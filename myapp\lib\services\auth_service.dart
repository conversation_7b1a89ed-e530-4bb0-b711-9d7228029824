import 'package:flutter/material.dart';
import '../models/user_model.dart';

// This is a simple in-memory authentication service for testing purposes
// In a real app, you would use a more robust solution with secure storage
class AuthService extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;

  // Dummy users for testing
  final List<UserModel> _dummyUsers = [
    UserModel(
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      profileImage: 'assets/images/admin_avatar.png',
    ),
    UserModel(
      id: '2',
      name: 'Client User',
      email: '<EMAIL>',
      role: 'client',
      profileImage: 'assets/images/client_avatar.png',
    ),
    UserModel(
      id: '3',
      name: 'Health Worker',
      email: '<EMAIL>',
      role: 'healthworker',
      profileImage: 'assets/images/healthworker_avatar.png',
    ),
  ];

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;
  bool get isAdmin => _currentUser?.role == 'admin';
  bool get isClient => _currentUser?.role == 'client';
  bool get isHealthWorker => _currentUser?.role == 'healthworker';

  // Login with email (simulated)
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Find user by email (in a real app, you would validate the password too)
    final user = _dummyUsers.firstWhere(
      (user) => user.email.toLowerCase() == email.toLowerCase(),
      orElse: () => throw Exception('User not found'),
    );

    _currentUser = user;
    _isLoading = false;
    notifyListeners();
    return true;
  }

  // Login as a specific role (for testing)
  Future<bool> loginAsRole(String role) async {
    _isLoading = true;
    notifyListeners();

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Find user by role
    final user = _dummyUsers.firstWhere(
      (user) => user.role == role,
      orElse: () => throw Exception('User with role $role not found'),
    );

    _currentUser = user;
    _isLoading = false;
    notifyListeners();
    return true;
  }

  // Logout
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    _currentUser = null;
    _isLoading = false;
    notifyListeners();
  }
}
