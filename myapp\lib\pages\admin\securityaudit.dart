import 'package:flutter/material.dart';

class SecurityAuditDialog extends StatefulWidget {
  const SecurityAuditDialog({Key? key}) : super(key: key);

  @override
  State<SecurityAuditDialog> createState() => _SecurityAuditDialogState();
}

class _SecurityAuditDialogState extends State<SecurityAuditDialog> {
  // Checkboxes state
  bool _userAccessAudit = true;
  bool _dataEncryptionCheck = true;
  bool _securityLogAnalysis = true;
  bool _vulnerabilityAssessment = true;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Security Audit',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Warning notice with orange background
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.orange.shade700,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Security Audit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This process will scan all system components and may take several minutes to complete.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange.shade900,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Audit options with checkboxes
            CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text(
                'User Access Audit',
                style: TextStyle(fontSize: 15),
              ),
              value: _userAccessAudit,
              onChanged: (bool? value) {
                setState(() {
                  _userAccessAudit = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),

            CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text(
                'Data Encryption Check',
                style: TextStyle(fontSize: 15),
              ),
              value: _dataEncryptionCheck,
              onChanged: (bool? value) {
                setState(() {
                  _dataEncryptionCheck = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),

            CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text(
                'Security Log Analysis',
                style: TextStyle(fontSize: 15),
              ),
              value: _securityLogAnalysis,
              onChanged: (bool? value) {
                setState(() {
                  _securityLogAnalysis = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),

            CheckboxListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text(
                'Vulnerability Assessment',
                style: TextStyle(fontSize: 15),
              ),
              value: _vulnerabilityAssessment,
              onChanged: (bool? value) {
                setState(() {
                  _vulnerabilityAssessment = value ?? false;
                });
              },
              controlAffinity: ListTileControlAffinity.leading,
              dense: true,
            ),

            const SizedBox(height: 24),

            // Start Audit Button
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton(
                onPressed: () {
                  _startAudit();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepOrange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 20,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Start Audit',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _startAudit() {
    // This will be connected to backend later
    final auditConfig = {
      'userAccessAudit': _userAccessAudit,
      'dataEncryptionCheck': _dataEncryptionCheck,
      'securityLogAnalysis': _securityLogAnalysis,
      'vulnerabilityAssessment': _vulnerabilityAssessment,
    };

    // debugPrint('Starting audit with config: $auditConfig');

    // Show loading indicator or feedback that audit has started
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Security audit has started. This may take several minutes.',
        ),
        duration: Duration(seconds: 3),
      ),
    );

    // Close dialog once audit is initiated
    Navigator.of(context).pop(auditConfig);
  }
}

// Example usage in your app:
void showSecurityAuditDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return const SecurityAuditDialog();
    },
  );
}

// Optional: Audit progress dialog that could be shown after starting the audit
class AuditProgressDialog extends StatefulWidget {
  final Map<String, bool> auditConfig;

  const AuditProgressDialog({Key? key, required this.auditConfig})
    : super(key: key);

  @override
  State<AuditProgressDialog> createState() => _AuditProgressDialogState();
}

class _AuditProgressDialogState extends State<AuditProgressDialog> {
  int _progress = 0;

  @override
  void initState() {
    super.initState();
    // Simulate audit progress
    _simulateProgress();
  }

  void _simulateProgress() {
    Future.delayed(const Duration(seconds: 1), () {
      if (_progress < 100) {
        setState(() {
          _progress += 5;
        });
        _simulateProgress();
      } else {
        // Audit complete
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Security Audit in Progress'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Please wait while the system is being audited...'),
          const SizedBox(height: 16),
          LinearProgressIndicator(value: _progress / 100),
          const SizedBox(height: 8),
          Text('$_progress% Complete'),
        ],
      ),
    );
  }
}

// To show the progress dialog after starting the audit:
void showAuditProgressDialog(
  BuildContext context,
  Map<String, bool> auditConfig,
) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AuditProgressDialog(auditConfig: auditConfig);
    },
  );
}
