import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.orange,
        scaffoldBackgroundColor: Colors.white,
      ),
      home: const FoodAllergiesScreen(),
    );
  }
}

class FoodAllergiesScreen extends StatefulWidget {
  const FoodAllergiesScreen({Key? key}) : super(key: key);

  @override
  _FoodAllergiesScreenState createState() => _FoodAllergiesScreenState();
}

class _FoodAllergiesScreenState extends State<FoodAllergiesScreen> {
  // Enhanced allergy categories with icons and descriptions
  Map<String, Map<String, dynamic>> allergies = {
    'Peanuts': {
      'selected': false,
      'icon': Icons.warning,
      'description': 'Peanut and peanut-based products',
      'severity': 'High',
    },
    'Tree Nuts': {
      'selected': false,
      'icon': Icons.eco,
      'description': 'Almonds, walnuts, cashews, etc.',
      'severity': 'High',
    },
    'Dairy': {
      'selected': false,
      'icon': Icons.local_drink,
      'description': 'Milk, cheese, yogurt, butter',
      'severity': 'Medium',
    },
    'Eggs': {
      'selected': false,
      'icon': Icons.egg,
      'description': 'Chicken eggs and egg products',
      'severity': 'Medium',
    },
    'Gluten': {
      'selected': false,
      'icon': Icons.grain,
      'description': 'Wheat, barley, rye products',
      'severity': 'Medium',
    },
    'Soy': {
      'selected': false,
      'icon': Icons.grass,
      'description': 'Soybeans and soy products',
      'severity': 'Low',
    },
    'Fish': {
      'selected': false,
      'icon': Icons.set_meal,
      'description': 'All fish and seafood',
      'severity': 'High',
    },
    'Shellfish': {
      'selected': false,
      'icon': Icons.restaurant,
      'description': 'Shrimp, crab, lobster, etc.',
      'severity': 'High',
    },
  };

  List<String> dietaryRestrictions = [];
  String selectedSeverity = 'All';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Back button and progress indicator
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Stack(
                      children: [
                        Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: 0.5, // First step progress
                          child: Container(
                            height: 4,
                            decoration: BoxDecoration(
                              color: const Color(0xFFFF7A5C), // Orange color
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Title text
              const Text(
                'Tell us about specific food allergies you have?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              // Subtitle
              Text(
                'Share your food allergies as we make your personalized approach to your preferred diet.',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 24),

              // Allergy options
              Expanded(
                child: ListView.builder(
                  itemCount: allergies.length,
                  itemBuilder: (context, index) {
                    String allergyName = allergies.keys.elementAt(index);
                    Map<String, dynamic> allergyData = allergies[allergyName]!;
                    bool isSelected = allergyData['selected'] as bool;

                    // Orange theme for selected items
                    Color bgColor =
                        isSelected
                            ? const Color(
                              0xFFFFF0E6,
                            ) // Light orange for selected items
                            : const Color(
                              0xFFFAF7F5,
                            ); // Light beige for unselected items

                    Color borderColor =
                        isSelected
                            ? const Color(
                              0xFFFF7A5C,
                            ) // Orange border for selected items
                            : Colors.transparent;

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: bgColor,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: borderColor,
                            width: isSelected ? 2 : 0,
                          ),
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16.0,
                          ),
                          leading: Icon(
                            allergyData['icon'] as IconData,
                            color:
                                isSelected
                                    ? const Color(0xFFFF7A5C)
                                    : Colors.grey.shade600,
                            size: 24,
                          ),
                          title: Text(
                            allergyName,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          subtitle: Text(
                            allergyData['description'] as String,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          trailing: Checkbox(
                            value: isSelected,
                            onChanged: (bool? value) {
                              setState(() {
                                allergies[allergyName]!['selected'] = value!;
                              });
                            },
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                            activeColor: const Color(
                              0xFFFF7A5C,
                            ), // Orange checkbox
                          ),
                          onTap: () {
                            setState(() {
                              allergies[allergyName]!['selected'] =
                                  !(allergies[allergyName]!['selected']
                                      as bool);
                            });
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Next button
              Container(
                width: double.infinity,
                height: 50,
                margin: const EdgeInsets.only(top: 16),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/diet-nutrition');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFF5C1C), // Bright orange
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const Text(
                    'Next',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
