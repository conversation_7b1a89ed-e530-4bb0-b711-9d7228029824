import 'package:flutter/material.dart';
import 'package:myapp/pages/Clients/Signup.dart';
import 'package:myapp/pages/Clients/Verify email.dart';
import 'package:myapp/pages/Clients/resetpassword.dart';
import 'package:myapp/pages/Clients/Home.dart';
// We're using our new login screen instead of the old one
import 'package:myapp/pages/Clients/cycle_tracking.dart';
import 'package:myapp/pages/Clients/contraceptive.dart';
import 'package:myapp/pages/Clients/Health_resource.dart';
import 'package:myapp/pages/Clients/Expert.dart';
import 'package:myapp/pages/Clients/Privacy.dart';
import 'package:myapp/pages/Clients/daily_tracking.dart' as daily;
import 'package:myapp/pages/Clients/Insight.dart' as insight;
import 'package:myapp/pages/Clients/calender.dart' as calendar;
import 'package:myapp/pages/Clients/user_profile.dart';
import 'package:myapp/pages/Clients/Loading.dart';
import 'package:myapp/pages/Clients/splash_slideshow.dart';
import 'package:myapp/pages/Clients/Allergies_nutrition.dart';
import 'package:myapp/pages/Clients/diet_Nutrition.dart';
import 'package:myapp/pages/Clients/Nutritious_Msg.dart';
import 'package:myapp/pages/Clients/Predictions.dart';
import 'package:myapp/pages/Clients/Symptoms.dart';
import 'package:myapp/pages/Clients/Insight2.dart';
import 'package:myapp/pages/Clients/Reminders.dart';
import 'package:myapp/pages/Clients/birth_homepage.dart';
import 'package:myapp/pages/Clients/birth_controllearn.dart';
import 'package:myapp/pages/Clients/birthcontroltrack.dart';
import 'package:myapp/pages/Clients/Hormonal.dart';
import 'package:myapp/pages/Clients/support.dart';
// Admin pages
import 'package:myapp/pages/admin/amindash.dart';
import 'package:myapp/pages/admin/Usermanagement.dart';
import 'package:myapp/pages/admin/adduser.dart';
import 'package:myapp/pages/admin/Generatereport.dart';
import 'package:myapp/pages/admin/datasecutiy.dart';
import 'package:myapp/pages/admin/Updates.dart';
import 'package:myapp/pages/admin/Analytics.dart';
import 'package:myapp/pages/health_worker/welcome.dart';

// Auth and routing
import 'package:myapp/pages/login_screen.dart';
import 'package:myapp/providers/auth_provider.dart';
import 'package:myapp/services/auth_service.dart';
import 'package:myapp/utils/route_guard.dart';

void main() {
  // Create the auth service
  final authService = AuthService();

  runApp(MyApp(authService: authService));
}

class MyApp extends StatelessWidget {
  final AuthService authService;

  const MyApp({super.key, required this.authService});

  @override
  Widget build(BuildContext context) {
    return AuthProvider(
      authService: authService,
      child: MaterialApp(
        title: 'Reproductive Health App',
        theme: ThemeData(
          primarySwatch: Colors.orange,
          primaryColor: const Color(0xFFFF7E34),
        ),
        initialRoute: '/',
        routes: {
          // Loading screen
          '/': (context) => const SplashWrapper(),

          // Splash slideshow
          '/splash': (context) => const SplashSlideshow(),

          // Auth routes
          '/login': (context) => const LoginScreen(),

          // Client routes
          '/signup': (context) => SignupScreen(),
          '/verify-email': (context) => EmailVerificationScreen(),
          '/reset-password': (context) => ResetPasswordScreen(),
          '/client-home': (context) => FamilyPlanningApp(),
          '/cycle-tracking': (context) => CycleTrackingScreen(),
          '/contraceptive': (context) => ContraceptiveMethodsPage(),
          '/health-resource': (context) => HealthResourcesScreen(),
          '/expert': (context) => ExpertSupportScreen(),
          '/privacy': (context) => PrivacySecurityPages(),

          // Cycle tracking related routes
          '/daily-tracking': (context) => daily.DailyLogScreen(),
          '/insights': (context) => insight.CycleTrackingScreen(),
          '/calendar': (context) => calendar.CycleTrackingCalendarPage(),

          // User profile
          '/user-profile': (context) => const UserProfileScreen(),

          // Nutrition and wellness routes
          '/allergies-nutrition': (context) => const FoodAllergiesScreen(),
          '/diet-nutrition': (context) => const DietPreferenceScreen(),
          '/nutritious-msg': (context) => const NutrimateScreen(),

          // Cycle tracking extended routes
          '/predictions': (context) => const PredictionsScreen(),
          '/symptoms': (context) => const SymptomsScreen(),
          '/insight2': (context) => const InsightsScreen(),
          '/reminders': (context) => const RemindersScreen(),

          // Birth control routes
          '/hormonal': (context) => const HormonalMethodsScreen(),
          '/birth-homepage': (context) => const CombinedHealthScreen(),
          '/birth-control-learn': (context) => const BirthControlLearn(),
          '/birth-control-track': (context) => const Controltrack(),

          // Support and other routes
          '/support': (context) => const Support(),


          // Admin routes (protected by RoleGuard)
          '/admin-dashboard':
              (context) => RoleGuard(
                allowedRoles: ['admin'],
                child: const AdminDashboard(),
              ),
          '/user-management':
              (context) => RoleGuard(
                allowedRoles: ['admin'],
                child: const UserManagementScreen(),
              ),
          '/add-user':
              (context) =>
                  RoleGuard(allowedRoles: ['admin'], child: const AddNewUser()),
          '/generate-report':
              (context) => RoleGuard(
                allowedRoles: ['admin'],
                child: const GenerateReportDialog(),
              ),
          '/data-security':
              (context) => RoleGuard(
                allowedRoles: ['admin'],
                child: const DataSecurityPage(),
              ),
          '/system-updates':
              (context) => RoleGuard(
                allowedRoles: ['admin'],
                child: const SystemUpdatesPage(),
              ),
          '/analytics':
              (context) => RoleGuard(
                allowedRoles: ['admin'],
                child: const AnalyticsReportsScreen(),
              ),

          // Health worker routes
          '/healthworker-dashboard':
              (context) => RoleGuard(
                allowedRoles: ['healthworker'],
                child: const HealthWorkerDashboard(),
              ),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class SplashWrapper extends StatefulWidget {
  const SplashWrapper({super.key});

  @override
  State<SplashWrapper> createState() => _SplashWrapperState();
}

class _SplashWrapperState extends State<SplashWrapper> {
  @override
  void initState() {
    super.initState();
    // Navigate to splash slideshow after 6 seconds (increased for better animation viewing)
    Future.delayed(const Duration(seconds: 6), () {
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/splash');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return const Loading();
  }
}
