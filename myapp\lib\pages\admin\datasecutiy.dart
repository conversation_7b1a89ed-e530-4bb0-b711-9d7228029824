import 'package:flutter/material.dart';
import 'securityaudit.dart';
import 'viewprotectionpolicy.dart';

class DataSecurityPage extends StatelessWidget {
  const DataSecurityPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF7F7F7),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Text(
                        'Data Security',
                        style: TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      Text(
                        'Manage security and compliance',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF888888),
                        ),
                      ),
                      SizedBox(height: 16),

                      // Security Status Card
                      _buildWhiteCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFE8F5E9),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.shield_outlined,
                                    color: Color(0xFF4CAF50),
                                    size: 24,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Security Status',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF333333),
                                      ),
                                    ),
                                    Text(
                                      'System is secure and compliant',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF888888),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: _buildSecurityChecksCard(
                                    title: 'Data Encryption',
                                    isComplete: true,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Expanded(
                                  child: _buildSecurityChecksCard(
                                    title: 'Access Controls',
                                    isComplete: true,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: _buildSecurityChecksCard(
                                    title: 'Audit Logs',
                                    isComplete: true,
                                  ),
                                ),
                                SizedBox(width: 12),
                                Expanded(
                                  child: _buildSecurityChecksCard(
                                    title: 'Vulnerability Scan',
                                    isComplete: false,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 16),

                      // Recent Security Alerts
                      Text(
                        'Recent Security Alerts',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 12),

                      // Multiple login attempts alert
                      _buildAlertCard(
                        color: Color(0xFFFFF9C4),
                        iconColor: Color(0xFFFBC02D),
                        icon: Icons.warning_amber_rounded,
                        title: 'Multiple login attempts',
                        description:
                            '3 failed login attempts from IP ************',
                        timeAgo: 'Today, 10:33 AM',
                      ),

                      SizedBox(height: 10),

                      // Unusual data access pattern alert
                      _buildAlertCard(
                        color: Color(0xFFFFEBEE),
                        iconColor: Color(0xFFE57373),
                        icon: Icons.error_outline,
                        title: 'Unusual data access pattern',
                        description:
                            'User ID 342 accessed sensitive data outside normal hours',
                        timeAgo: 'Yesterday, 11:52 PM',
                      ),

                      SizedBox(height: 16),

                      // Privacy Metrics
                      Text(
                        'Privacy Metrics',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 12),

                      // Privacy Metrics Cards
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: _buildWhiteCard(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Data Protection',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF333333),
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Encryption Level',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF888888),
                                        ),
                                      ),
                                      Text(
                                        '256-bit',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFFFF6B35),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Last Audit',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF888888),
                                        ),
                                      ),
                                      Text(
                                        '2 days ago',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF333333),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          Expanded(
                            child: _buildWhiteCard(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Access Control',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF333333),
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Active Sessions',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF888888),
                                        ),
                                      ),
                                      Text(
                                        '42',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF333333),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Failed Attempts',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF888888),
                                        ),
                                      ),
                                      Text(
                                        '3',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFFE57373),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 16),

                      // Compliance
                      Text(
                        'Compliance',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 12),

                      // Data Protection Policy
                      _buildComplianceCard(
                        icon: Icons.description,
                        iconColor: Color(0xFFFF9800),
                        title: 'Data Protection Policy',
                        subtitle: 'Last updated: June 15, 2023',
                        actionText: 'View',
                      ),

                      SizedBox(height: 10),

                      // Privacy Compliance
                      _buildComplianceCard(
                        icon: Icons.lock,
                        iconColor: Color(0xFFFF6B35),
                        title: 'Privacy Compliance',
                        subtitle: 'HIPAA, GDPR compliant',
                        actionText: 'Verify',
                      ),

                      SizedBox(height: 16),

                      // Run Security Audit Button
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFFFF6B35),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () {
                            // Show the security audit dialog
                            showSecurityAuditDialog(context);
                          },
                          child: Text(
                            'Run Security Audit',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom Navigation
            _buildBottomNavigation(),
          ],
        ),
      ),
    );
  }

  Widget _buildWhiteCard({required Widget child, EdgeInsetsGeometry? padding}) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      padding: padding ?? EdgeInsets.all(16),
      child: child,
    );
  }

  Widget _buildSecurityChecksCard({
    required String title,
    required bool isComplete,
  }) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFEEEEEE)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isComplete ? Color(0xFFE8F5E9) : Color(0xFFFFF9C4),
              shape: BoxShape.circle,
            ),
            child: Icon(
              isComplete
                  ? Icons.check_circle_outline
                  : Icons.warning_amber_rounded,
              color: isComplete ? Color(0xFF4CAF50) : Color(0xFFFBC02D),
              size: 20,
            ),
          ),
          SizedBox(height: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard({
    required Color color,
    required Color iconColor,
    required IconData icon,
    required String title,
    required String description,
    required String timeAgo,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: iconColor, size: 24),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Color(0xFF666666)),
                ),
                SizedBox(height: 4),
                Text(
                  timeAgo,
                  style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Show Data Protection Policy Dialog
  void _showDataProtectionPolicyDialog(BuildContext context) {
    // Use the DataProtectionPolicyDialog from viewprotectionpolicy.dart
    showDataProtectionPolicyDialog(context);
  }

  Widget _buildComplianceCard({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String actionText,
  }) {
    return Builder(
      builder: (BuildContext context) {
        return _buildWhiteCard(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: iconColor.withAlpha(26), // 0.1 * 255 ≈ 26
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: iconColor, size: 24),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF333333),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () {
                  // Show the appropriate dialog based on the title
                  if (title == 'Data Protection Policy') {
                    _showDataProtectionPolicyDialog(context);
                  } else if (title == 'Privacy Compliance') {
                    // Handle Privacy Compliance verification
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Verifying compliance status...'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                },
                child: Text(
                  actionText,
                  style: TextStyle(
                    color: Color(0xFFFF6B35),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomNavigation() {
    return Builder(
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                blurRadius: 5,
                spreadRadius: 0,
                offset: Offset(0, -1),
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(context, Icons.home, 'Home'),
              _buildNavItem(context, Icons.people, 'Users'),
              _buildNavItem(context, Icons.shield, 'Security', isActive: true),
              _buildNavItem(context, Icons.sync, 'Updates'),
              _buildNavItem(context, Icons.bar_chart, 'Reports'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    IconData icon,
    String label, {
    bool isActive = false,
  }) {
    Color color = isActive ? Color(0xFFFF6B35) : Color(0xFF888888);
    return InkWell(
      onTap: () {
        if (label == 'Home') {
          Navigator.pushNamed(context, '/admin-dashboard');
        } else if (label == 'Users') {
          Navigator.pushNamed(context, '/user-management');
        } else if (label == 'Reports') {
          Navigator.pushNamed(context, '/analytics');
        } else if (label == 'Updates') {
          // Navigate to System Updates page
          Navigator.pushNamed(context, '/system-updates');
        }
        // No action for Security since we're already on that page
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          Text(label, style: TextStyle(color: color, fontSize: 12)),
        ],
      ),
    );
  }
}
