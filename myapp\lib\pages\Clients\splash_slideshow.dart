import 'package:flutter/material.dart';
import 'splash_page_1.dart';
import 'splash_page_2.dart';
import 'splash_page_3.dart';

class SplashSlideshow extends StatefulWidget {
  const SplashSlideshow({super.key});

  @override
  State<SplashSlideshow> createState() => _SplashSlideshowState();
}

class _SplashSlideshowState extends State<SplashSlideshow> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _startAutoSlide();
  }

  void _startAutoSlide() {
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && _currentPage < 2) {
        _nextPage();
        _startAutoSlide();
      }
    });
  }

  void _nextPage() {
    if (_currentPage < 2) {
      setState(() {
        _currentPage++;
      });
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
      });
      _pageController.animateToPage(
        _currentPage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentPage = index;
          });
        },
        children: [
          // Page 1
          SplashPageWrapper(
            onNext: _nextPage,
            onPrevious: null,
            showNext: true,
            showPrevious: false,
            child: const SplashPage1(),
          ),
          // Page 2
          SplashPageWrapper(
            onNext: _nextPage,
            onPrevious: _previousPage,
            showNext: true,
            showPrevious: true,
            child: const SplashPage2(),
          ),
          // Page 3
          SplashPageWrapper(
            onNext: null,
            onPrevious: _previousPage,
            showNext: false,
            showPrevious: true,
            child: const SplashPage3(),
          ),
        ],
      ),
    );
  }
}

class SplashPageWrapper extends StatelessWidget {
  final Widget child;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;
  final bool showNext;
  final bool showPrevious;

  const SplashPageWrapper({
    super.key,
    required this.child,
    this.onNext,
    this.onPrevious,
    required this.showNext,
    required this.showPrevious,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (onNext != null) {
          onNext!();
        }
      },
      child: Stack(
        children: [
          child,
          // Navigation overlay buttons (invisible but functional)
          if (showPrevious)
            Positioned(
              bottom: 80,
              left: 24,
              child: GestureDetector(
                onTap: onPrevious,
                child: Container(
                  width: 100,
                  height: 50,
                  color: Colors.transparent,
                ),
              ),
            ),
          if (showNext)
            Positioned(
              bottom: 80,
              right: 24,
              child: GestureDetector(
                onTap: onNext,
                child: Container(
                  width: 100,
                  height: 50,
                  color: Colors.transparent,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
