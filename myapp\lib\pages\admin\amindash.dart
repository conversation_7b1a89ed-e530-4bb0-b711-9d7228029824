import 'package:flutter/material.dart';
import 'adduser.dart';
import 'Generatereport.dart';
import 'Usermanagement.dart';

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // App Bar Title
                Text(
                  'ComFP Admin',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                Text(
                  'Family Planning Support System',
                  style: TextStyle(fontSize: 14, color: Color(0xFF888888)),
                ),
                SizedBox(height: 20),

                // System Overview Cards
                GridView.count(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 1.5,
                  children: [
                    _buildStatCard(
                      icon: Icons.people_alt_outlined,
                      color: Color(0xFFFF6B35),
                      title: 'Active Users',
                      value: '1,248',
                    ),
                    _buildStatCard(
                      icon: Icons.warning_amber_rounded,
                      color: Color(0xFFFFA400),
                      title: 'Security Alerts',
                      value: '3',
                    ),
                    _buildStatCard(
                      icon: Icons.notifications_none_outlined,
                      color: Color(0xFF4ECB71),
                      title: 'Notifications',
                      value: '7',
                    ),
                    _buildStatCard(
                      icon: Icons.bar_chart_outlined,
                      color: Color(0xFF7B61FF),
                      title: 'Reports',
                      value: '12',
                    ),
                    _buildStatCard(
                      icon: Icons.medical_services_outlined,
                      color: Color(0xFFFF4D4D),
                      title: 'Health Providers',
                      value: '156',
                    ),
                    _buildStatCard(
                      icon: Icons.book_outlined,
                      color: Color(0xFF2196F3),
                      title: 'Resources',
                      value: '324',
                    ),
                  ],
                ),

                SizedBox(height: 20),

                // Program Statistics
                _buildSectionTitle('Program Statistics'),
                Row(
                  children: [
                    Expanded(
                      child: _buildProgressCard(
                        title: 'User Demographics',
                        items: [
                          _buildProgressItem(
                            'Rural Users',
                            0.68,
                            Color(0xFFFF6B35),
                            '68%',
                          ),
                          _buildProgressItem(
                            'Urban Users',
                            0.32,
                            Color(0xFFFF6B35),
                            '32%',
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: _buildProgressCard(
                        title: 'Feature Usage',
                        items: [
                          _buildProgressItem(
                            'Voice Guidance',
                            0.75,
                            Color(0xFFFF6B35),
                            '75%',
                          ),
                          _buildProgressItem(
                            'Offline Access',
                            0.54,
                            Color(0xFFFF6B35),
                            '54%',
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20),

                // Recent Activity
                _buildSectionTitle('Recent Activity'),
                _buildActivityCard('New user registration', '2 hours ago'),
                _buildActivityCard(
                  'Educational content updated',
                  '4 hours ago',
                ),
                _buildActivityCard('Community outreach milestone', 'Yesterday'),
                _buildActivityCard(
                  'Voice guidance feature update',
                  '2 days ago',
                ),

                SizedBox(height: 20),

                // Quick Actions
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Show the Add New User dialog
                          showDialog(
                            context: context,
                            builder: (context) => const AddNewUser(),
                          );
                        },
                        icon: Icon(Icons.add, color: Colors.white),
                        label: Text(
                          'Add New User',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFFFF6B35),
                          padding: EdgeInsets.symmetric(vertical: 15),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          // Show the Generate Report dialog
                          showDialog(
                            context: context,
                            builder: (context) => const GenerateReportDialog(),
                          );
                        },
                        icon: Icon(Icons.document_scanner, color: Colors.white),
                        label: Text(
                          'Generate Report',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey,
                          padding: EdgeInsets.symmetric(vertical: 15),
                        ),
                      ),
                    ),
                  ],
                ),

                // Bottom Navigation
                SizedBox(height: 20),
                _buildBottomNavigation(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required Color color,
    required String title,
    required String value,
  }) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 30),
            SizedBox(height: 10),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Color(0xFF333333),
        ),
      ),
    );
  }

  Widget _buildProgressCard({
    required String title,
    required List<Widget> items,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
            SizedBox(height: 10),
            ...items,
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(
    String label,
    double progress,
    Color color,
    String percentage,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
              ),
              Text(
                percentage,
                style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
              ),
            ],
          ),
          SizedBox(height: 5),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade300,
            color: color,
            minHeight: 6,
            borderRadius: BorderRadius.circular(3),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityCard(String title, String timestamp) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.symmetric(vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(fontSize: 14, color: Color(0xFF333333)),
        ),
        subtitle: Text(
          timestamp,
          style: TextStyle(fontSize: 12, color: Color(0xFF888888)),
        ),
        trailing: Icon(Icons.chevron_right, color: Color(0xFF888888)),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Builder(
      builder: (BuildContext context) {
        return BottomAppBar(
          color: Colors.white,
          elevation: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(context, Icons.home, 'Home', isActive: true),
              _buildNavItem(context, Icons.people, 'Users'),
              _buildNavItem(context, Icons.security, 'Security'),
              _buildNavItem(context, Icons.sync, 'Updates'),
              _buildNavItem(context, Icons.bar_chart, 'Reports'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    IconData icon,
    String label, {
    bool isActive = false,
  }) {
    return InkWell(
      onTap: () {
        if (label == 'Users') {
          // Navigate to User Management page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const UserManagementScreen(),
            ),
          );
        } else if (label == 'Security') {
          // Navigate to Data Security page
          Navigator.pushNamed(context, '/data-security');
        } else if (label == 'Updates') {
          // Navigate to System Updates page
          Navigator.pushNamed(context, '/system-updates');
        } else if (label == 'Reports') {
          // Navigate to Analytics page
          Navigator.pushNamed(context, '/analytics');
        }
        // No action for Home since we're already on that page
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isActive ? Color(0xFFFF6B35) : Color(0xFF888888),
            size: 24,
          ),
          Text(
            label,
            style: TextStyle(
              color: isActive ? Color(0xFFFF6B35) : Color(0xFF888888),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}
